# Windows Enhancer

A modern Windows enhancement utility built with Kotlin Compose Multiplatform for desktop.

## Features

- **Dashboard**: Overview of system status and quick actions
- **System Optimization**: Performance tuning and system cleanup tools
- **Privacy & Security**: Privacy protection and security features
- **Settings**: Application configuration and preferences

## Technology Stack

- **Kotlin**: Modern programming language
- **Compose Multiplatform**: Declarative UI framework
- **Material Design 3**: Modern design system
- **Gradle**: Build automation

## Requirements

- Java 8 or higher (Java 17+ recommended)
- Windows 10/11
- Gradle (or use the included wrapper)

## Getting Started

### Quick Start

1. **Check Java Installation**:
   ```cmd
   java -version
   ```
   If Java is not installed, download it from [Adoptium](https://adoptium.net/temurin/releases/)

2. **Run the Application**:
   - **Option A**: Use the batch file (recommended for beginners)
     ```cmd
     run.bat
     ```
   - **Option B**: Use PowerShell
     ```powershell
     .\run.ps1
     ```
   - **Option C**: Use Gradle directly
     ```cmd
     .\gradlew.bat run
     ```

### First-Time Setup

If you encounter issues, run the setup script:
```cmd
setup.bat
```

This will check your environment and guide you through any missing requirements.

### Building the Application

1. Clone the repository
2. Open a terminal in the project directory
3. Run the application:
   ```cmd
   .\gradlew.bat run
   ```

### Creating a Distribution

To create a distributable package:

```cmd
.\gradlew.bat packageDistributionForCurrentOS
```

This will create:
- Windows: `.msi` and `.exe` installers in `build/compose/binaries/main/`

### Development

To run the application in development mode:

```cmd
.\gradlew.bat run
```

For continuous development with auto-reload:
```cmd
.\gradlew.bat run --continuous
```

## Project Structure

```
src/
├── main/
│   ├── kotlin/
│   │   ├── Main.kt                 # Application entry point
│   │   └── ui/
│   │       ├── App.kt              # Main application UI
│   │       ├── components/         # Reusable UI components
│   │       │   ├── NavigationRail.kt
│   │       │   ├── FeatureCard.kt
│   │       │   └── SystemInfoPanel.kt
│   │       └── theme/
│   │           └── Theme.kt        # Material Design theme
│   └── resources/
│       └── icon.png               # Application icon
```

## Features Overview

### Dashboard
- Real-time system monitoring (CPU, Memory, Disk usage)
- Quick access to common optimization tasks
- System status overview

### System Optimization
- Performance boost utilities
- Memory optimization
- Disk defragmentation
- Service management

### Privacy & Security
- Privacy scanner
- Data protection tools
- Network security monitoring
- Browser cleanup utilities

### Settings
- Application preferences
- Auto-start configuration
- Notification settings
- Theme selection

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with [Compose Multiplatform](https://github.com/JetBrains/compose-multiplatform)
- Uses [Material Design 3](https://m3.material.io/)
- Powered by [Kotlin](https://kotlinlang.org/)
