package ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay

@Composable
fun SystemInfoPanel(
    modifier: Modifier = Modifier
) {
    var cpuUsage by remember { mutableStateOf(0f) }
    var memoryUsage by remember { mutableStateOf(0f) }
    var diskUsage by remember { mutableStateOf(0f) }

    // Simulate system monitoring (in a real app, you'd get actual system data)
    LaunchedEffect(Unit) {
        while (true) {
            cpuUsage = (20..80).random().toFloat()
            memoryUsage = (30..70).random().toFloat()
            diskUsage = (40..60).random().toFloat()
            delay(2000)
        }
    }

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "System Status",
                style = MaterialTheme.typography.h5,
                fontWeight = FontWeight.Bold
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                SystemMetric(
                    label = "CPU Usage",
                    value = cpuUsage,
                    icon = Icons.Default.Memory,
                    modifier = Modifier.weight(1f)
                )

                SystemMetric(
                    label = "Memory",
                    value = memoryUsage,
                    icon = Icons.Default.Storage,
                    modifier = Modifier.weight(1f)
                )

                SystemMetric(
                    label = "Disk",
                    value = diskUsage,
                    icon = Icons.Default.Folder,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun SystemMetric(
    label: String,
    value: Float,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(32.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Text(
            text = label,
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        LinearProgressIndicator(
            progress = value / 100f,
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
        )

        Text(
            text = "${value.toInt()}%",
            style = MaterialTheme.typography.caption,
            fontWeight = FontWeight.Medium
        )
    }
}
