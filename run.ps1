# Windows Enhancer - PowerShell Runner
Write-Host "Windows Enhancer - <PERSON><PERSON><PERSON>" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host ""

Write-Host "Checking Java installation..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1
    Write-Host "Java found: $($javaVersion[0])" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Java is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Java 8 or higher and try again" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Checking Gradle wrapper..." -ForegroundColor Yellow
if (-not (Test-Path "gradle\wrapper\gradle-wrapper.jar")) {
    Write-Host "ERROR: Gradle wrapper not found" -ForegroundColor Red
    Write-Host "Please install Gradle and run: gradle wrapper" -ForegroundColor Red
    Write-Host "Or download the project with a complete Gradle wrapper." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Gradle wrapper found!" -ForegroundColor Green
Write-Host ""
Write-Host "Building and running the application..." -ForegroundColor Yellow

& .\gradlew.bat run

Read-Host "Press Enter to exit"
