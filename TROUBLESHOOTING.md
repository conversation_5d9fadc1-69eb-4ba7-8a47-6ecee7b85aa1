# Troubleshooting Guide

## Common Issues and Solutions

### 1. Java Not Found

**Error**: `'java' is not recognized as an internal or external command`

**Solution**:
1. Install Java 8 or higher from [Adoptium](https://adoptium.net/temurin/releases/)
2. Make sure Java is added to your PATH environment variable
3. Restart your command prompt/PowerShell
4. Verify with: `java -version`

### 2. Gradle Wrapper Issues

**Error**: `Could not find or load main class org.gradle.wrapper.GradleWrapperMain`

**Solution**:
1. **Option A**: Install Gradle and regenerate wrapper
   ```cmd
   # Install Gradle from https://gradle.org/install/
   gradle wrapper
   ```

2. **Option B**: Download wrapper from another project
   - Copy `gradle-wrapper.jar` from another Gradle project
   - Place it in `gradle/wrapper/gradle-wrapper.jar`

3. **Option C**: Use system Gradle
   ```cmd
   gradle run
   ```

### 3. Build Failures

**Error**: Various compilation errors

**Solutions**:
1. **Clean and rebuild**:
   ```cmd
   .\gradlew.bat clean build
   ```

2. **Check Java version compatibility**:
   - This project requires Java 8+
   - Verify with: `java -version`

3. **Clear Gradle cache**:
   ```cmd
   .\gradlew.bat clean --refresh-dependencies
   ```

### 4. Application Won't Start

**Error**: Application starts but shows errors or blank screen

**Solutions**:
1. **Check system requirements**:
   - Windows 10/11
   - Sufficient RAM (2GB+ recommended)
   - Graphics drivers up to date

2. **Run with debug info**:
   ```cmd
   .\gradlew.bat run --info
   ```

3. **Check for conflicting software**:
   - Antivirus blocking execution
   - Other Java applications interfering

### 5. Performance Issues

**Symptoms**: Slow startup, laggy UI, high memory usage

**Solutions**:
1. **Increase JVM memory**:
   - Edit `gradle.properties`
   - Increase: `org.gradle.jvmargs=-Xmx4g`

2. **Close other applications**:
   - Free up system resources
   - Close unnecessary programs

3. **Update graphics drivers**:
   - Ensure latest drivers are installed
   - Enable hardware acceleration if available

### 6. Distribution/Packaging Issues

**Error**: Failed to create installer packages

**Solutions**:
1. **Install required tools**:
   - Windows: WiX Toolset for MSI creation
   - Ensure tools are in PATH

2. **Check disk space**:
   - Ensure sufficient space for build artifacts
   - Clean temporary files

3. **Run with verbose output**:
   ```cmd
   .\gradlew.bat packageDistributionForCurrentOS --info
   ```

## Getting Help

If you're still experiencing issues:

1. **Check the logs**:
   - Look in `build/` directory for detailed logs
   - Check console output for error messages

2. **Verify environment**:
   - Run `setup.bat` to check your environment
   - Ensure all requirements are met

3. **Create an issue**:
   - Include your Java version: `java -version`
   - Include your OS version
   - Include the full error message
   - Include steps to reproduce the issue

## Useful Commands

```cmd
# Check Java version
java -version

# Check Gradle version
.\gradlew.bat --version

# Clean build
.\gradlew.bat clean

# Build without running
.\gradlew.bat build

# Run with debug info
.\gradlew.bat run --info --debug

# List all available tasks
.\gradlew.bat tasks

# Refresh dependencies
.\gradlew.bat build --refresh-dependencies
```

## Environment Setup Checklist

- [ ] Java 8+ installed and in PATH
- [ ] Gradle wrapper present (`gradle/wrapper/gradle-wrapper.jar`)
- [ ] Windows 10/11 operating system
- [ ] Sufficient disk space (1GB+ free)
- [ ] Sufficient RAM (2GB+ available)
- [ ] Updated graphics drivers
- [ ] No antivirus blocking Java/Gradle execution
