import org.jetbrains.compose.desktop.application.dsl.TargetFormat

plugins {
    kotlin("jvm") version "1.8.22"
    id("org.jetbrains.compose") version "1.4.3"
}

group = "com.windowsenhancer"
version = "1.0.0"

repositories {
    mavenCentral()
    maven("https://maven.pkg.jetbrains.space/public/p/compose/dev")
    google()
}

dependencies {
    // Compose BOM
    implementation(compose.desktop.currentOs)
    implementation(compose.material)
    implementation(compose.materialIconsExtended)

    // Kotlin Coroutines
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-swing:1.6.4")

    // Testing
    testImplementation(kotlin("test"))
}

compose.desktop {
    application {
        mainClass = "MainKt"

        nativeDistributions {
            targetFormats(TargetFormat.Dmg, TargetFormat.Msi, TargetFormat.Deb, TargetFormat.Exe)
            packageName = "Windows Enhancer"
            packageVersion = "1.0.0"
            description = "A Windows enhancement utility built with Kotlin Compose"
            copyright = "© 2024 Windows Enhancer. All rights reserved."
            vendor = "Windows Enhancer Team"

            windows {
                iconFile.set(project.file("src/main/resources/icon.ico"))
                menuGroup = "Windows Enhancer"
                // Add Windows-specific properties
                upgradeUuid = "61DAB35E-17CB-43B8-B24D-A9C57C7C6A9E"
            }
        }

        buildTypes.release.proguard {
            configurationFiles.from("compose-desktop.pro")
        }
    }
}

kotlin {
    jvmToolchain(8)
}

tasks.test {
    useJUnitPlatform()
}
