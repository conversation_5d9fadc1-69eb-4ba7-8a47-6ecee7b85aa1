import org.jetbrains.compose.desktop.application.dsl.TargetFormat

plugins {
    kotlin("jvm") version "1.9.21"
    id("org.jetbrains.compose") version "1.5.11"
}

group = "com.windowsenhancer"
version = "1.0.0"

repositories {
    mavenCentral()
    maven("https://maven.pkg.jetbrains.space/public/p/compose/dev")
    google()
}

dependencies {
    // Compose BOM
    implementation(compose.desktop.currentOs)
    implementation(compose.material3)
    implementation(compose.materialIconsExtended)

    // Kotlin Coroutines
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-swing:1.7.3")

    // Testing
    testImplementation(kotlin("test"))
}

compose.desktop {
    application {
        mainClass = "MainKt"

        nativeDistributions {
            targetFormats(TargetFormat.Dmg, TargetFormat.Msi, TargetFormat.Deb, TargetFormat.Exe)
            packageName = "Windows Enhancer"
            packageVersion = "1.0.0"
            description = "A Windows enhancement utility built with Kotl<PERSON> Compose"
            copyright = "© 2024 Windows Enhancer. All rights reserved."
            vendor = "Windows Enhancer Team"

            windows {
                iconFile.set(project.file("src/main/resources/icon.ico"))
                menuGroup = "Windows Enhancer"
                // Add Windows-specific properties
                upgradeUuid = "61DAB35E-17CB-43B8-B24D-A9C57C7C6A9E"
            }
        }

        buildTypes.release.proguard {
            configurationFiles.from("compose-desktop.pro")
        }
    }
}

kotlin {
    jvmToolchain(17)
}

tasks.test {
    useJUnitPlatform()
}
