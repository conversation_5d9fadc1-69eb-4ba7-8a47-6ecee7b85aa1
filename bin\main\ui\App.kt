package ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import ui.components.FeatureCard
import ui.components.NavigationRail
import ui.components.SystemInfoPanel

@Composable
fun App() {
    var selectedTab by remember { mutableStateOf(0) }

    Row(modifier = Modifier.fillMaxSize()) {
        // Navigation Rail
        NavigationRail(
            selectedTab = selectedTab,
            onTabSelected = { selectedTab = it }
        )

        // Main Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp)
        ) {
            when (selectedTab) {
                0 -> DashboardContent()
                1 -> SystemOptimizationContent()
                2 -> PrivacyContent()
                3 -> SettingsContent()
            }
        }
    }
}

@Composable
private fun DashboardContent() {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Windows Enhancer Dashboard",
            style = MaterialTheme.typography.h4,
            fontWeight = FontWeight.Bold
        )

        Text(
            text = "Welcome to your Windows enhancement toolkit",
            style = MaterialTheme.typography.body1,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(8.dp))

        // System Info Panel
        SystemInfoPanel()

        Spacer(modifier = Modifier.height(16.dp))

        // Quick Actions
        Text(
            text = "Quick Actions",
            style = MaterialTheme.typography.h6,
            fontWeight = FontWeight.SemiBold
        )

        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(quickActions) { action ->
                FeatureCard(
                    title = action.title,
                    description = action.description,
                    icon = action.icon,
                    onClick = action.onClick
                )
            }
        }
    }
}

@Composable
private fun SystemOptimizationContent() {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "System Optimization",
            style = MaterialTheme.typography.h4,
            fontWeight = FontWeight.Bold
        )

        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(optimizationFeatures) { feature ->
                FeatureCard(
                    title = feature.title,
                    description = feature.description,
                    icon = feature.icon,
                    onClick = feature.onClick
                )
            }
        }
    }
}

@Composable
private fun PrivacyContent() {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Privacy & Security",
            style = MaterialTheme.typography.h4,
            fontWeight = FontWeight.Bold
        )

        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(privacyFeatures) { feature ->
                FeatureCard(
                    title = feature.title,
                    description = feature.description,
                    icon = feature.icon,
                    onClick = feature.onClick
                )
            }
        }
    }
}

@Composable
private fun SettingsContent() {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Settings",
            style = MaterialTheme.typography.h4,
            fontWeight = FontWeight.Bold
        )

        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "Application Settings",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.SemiBold
                )

                var autoStart by remember { mutableStateOf(false) }
                var notifications by remember { mutableStateOf(true) }
                var darkMode by remember { mutableStateOf(false) }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("Start with Windows")
                    Switch(
                        checked = autoStart,
                        onCheckedChange = { autoStart = it }
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("Enable Notifications")
                    Switch(
                        checked = notifications,
                        onCheckedChange = { notifications = it }
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("Dark Mode")
                    Switch(
                        checked = darkMode,
                        onCheckedChange = { darkMode = it }
                    )
                }
            }
        }
    }
}

// Data classes for features
data class AppFeature(
    val title: String,
    val description: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector,
    val onClick: () -> Unit = {}
)

private val quickActions = listOf(
    AppFeature(
        title = "System Cleanup",
        description = "Clean temporary files and free up disk space",
        icon = Icons.Default.CleaningServices
    ),
    AppFeature(
        title = "Registry Optimization",
        description = "Optimize Windows registry for better performance",
        icon = Icons.Default.Tune
    ),
    AppFeature(
        title = "Startup Manager",
        description = "Manage programs that start with Windows",
        icon = Icons.Default.PlayArrow
    )
)

private val optimizationFeatures = listOf(
    AppFeature(
        title = "Performance Boost",
        description = "Optimize system performance and responsiveness",
        icon = Icons.Default.Speed
    ),
    AppFeature(
        title = "Memory Optimization",
        description = "Free up RAM and optimize memory usage",
        icon = Icons.Default.Memory
    ),
    AppFeature(
        title = "Disk Defragmentation",
        description = "Defragment and optimize disk drives",
        icon = Icons.Default.Storage
    ),
    AppFeature(
        title = "Service Manager",
        description = "Manage Windows services and processes",
        icon = Icons.Default.Settings
    )
)

private val privacyFeatures = listOf(
    AppFeature(
        title = "Privacy Scanner",
        description = "Scan for privacy issues and tracking",
        icon = Icons.Default.Security
    ),
    AppFeature(
        title = "Data Protection",
        description = "Protect sensitive files and folders",
        icon = Icons.Default.Shield
    ),
    AppFeature(
        title = "Network Security",
        description = "Monitor and secure network connections",
        icon = Icons.Default.NetworkCheck
    ),
    AppFeature(
        title = "Browser Cleanup",
        description = "Clear browser data and cookies",
        icon = Icons.Default.Web
    )
)
