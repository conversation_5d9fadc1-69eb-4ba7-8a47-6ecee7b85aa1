import androidx.compose.ui.res.painterResource
import androidx.compose.ui.window.Window
import androidx.compose.ui.window.application
import androidx.compose.ui.window.rememberWindowState
import androidx.compose.ui.unit.dp
import ui.App
import ui.theme.WindowsEnhancerTheme

fun main() = application {
    val windowState = rememberWindowState(
        width = 1200.dp,
        height = 800.dp
    )
    
    Window(
        onCloseRequest = ::exitApplication,
        title = "Windows Enhancer",
        state = windowState,
        icon = painterResource("icon.png")
    ) {
        WindowsEnhancerTheme {
            App()
        }
    }
}
