@echo off
echo Windows Enhancer - Ko<PERSON>in Compose App
echo =====================================
echo.

echo Checking Java installation...
java -version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 8 or higher and try again
    pause
    exit /b 1
)

echo.
echo Downloading Gradle wrapper if needed...
if not exist "gradle\wrapper\gradle-wrapper.jar" (
    echo Gradle wrapper not found. Please install Gradle and run: gradle wrapper
    echo Or download the project with a complete Gradle wrapper.
    pause
    exit /b 1
)

echo.
echo Building and running the application...
.\gradlew.bat run

pause
